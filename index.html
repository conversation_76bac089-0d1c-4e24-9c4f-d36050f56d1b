<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Advanced Vowel Counter Application - Highlight vowels, text-to-speech, and beautiful design. Made by RANIT MANIK.">
    <title>Advanced Vowel Counter</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- Notification container -->
    <div id="notification" class="notification"></div>

    <div class="container">
        <header>
            <div class="header-content">
                <h1>
                    <i class="fas fa-spell-check"></i>
                    Advanced Vowel Counter
                </h1>
                <p class="subtitle">Discover and highlight vowels in your text</p>
                <div class="vowel-indicators">
                    <div class="vowel-indicator">
                        <span class="vowel-letter a-vowel">A</span>
                        <span class="count" id="headerCountA">0</span>
                    </div>
                    <div class="vowel-indicator">
                        <span class="vowel-letter e-vowel">E</span>
                        <span class="count" id="headerCountE">0</span>
                    </div>
                    <div class="vowel-indicator">
                        <span class="vowel-letter i-vowel">I</span>
                        <span class="count" id="headerCountI">0</span>
                    </div>
                    <div class="vowel-indicator">
                        <span class="vowel-letter o-vowel">O</span>
                        <span class="count" id="headerCountO">0</span>
                    </div>
                    <div class="vowel-indicator">
                        <span class="vowel-letter u-vowel">U</span>
                        <span class="count" id="headerCountU">0</span>
                    </div>
                </div>
            </div>
            <button class="theme-toggle" onclick="toggleTheme()" title="Toggle theme">
                <i class="fas fa-moon"></i>
            </button>
        </header>

        <main>
            <div class="top-section">
                <div class="input-section">
                    <div class="input-header">
                        <label for="inputText">Enter your text:</label>
                        <div class="controls">
                            <button class="icon-btn" onclick="readText()" title="Read text aloud" id="readBtn">
                                <i class="fas fa-volume-up"></i>
                            </button>
                            <button class="icon-btn" onclick="clearText()" title="Clear text">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                    <textarea
                        name="inputText"
                        id="inputText"
                        autocapitalize="none"
                        placeholder="Type or paste your text here, then click Count to analyze vowels..."
                        autofocus="autofocus"
                        rows="6"
                    ></textarea>
                    <button class="count-btn" onclick="countVowels()" title="Count vowels">
                        <i class="fas fa-calculator"></i>
                        Count Vowels
                    </button>
                </div>

                <div class="highlighted-text-container">
                    <h3>Your Text:</h3>
                    <div id="highlightedText" class="highlighted-text">
                        Click "Count Vowels" to analyze your text...
                    </div>
                    <div class="total-stats">
                        <div class="total-item">
                            <span>Total Vowels:</span>
                            <span id="totalVowels">0</span>
                        </div>
                        <div class="total-item">
                            <span>Total Characters:</span>
                            <span id="totalChars">0</span>
                        </div>
                        <div class="total-item">
                            <span>Vowel Percentage:</span>
                            <span id="vowelPercentage">0%</span>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="script.js"></script>
</body>
</html>