// Global variables
let isDarkTheme = true;
let speechSynthesis = window.speechSynthesis;
let currentUtterance = null;

// Vowel colors for highlighting
const vowelColors = {
    'a': '#ff6b6b', // Red
    'e': '#4ecdc4', // Teal
    'i': '#45b7d1', // Blue
    'o': '#96ceb4', // Green
    'u': '#feca57'  // Yellow
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Set initial theme
    document.body.setAttribute('data-theme', 'dark');

    // Add some sample text for demonstration
    const sampleText = "Hello! Welcome to the Advanced Vowel Counter. This application will highlight all vowels in your text with beautiful colors.";
    // Uncomment the next line if you want to start with sample text
    // document.getElementById('inputText').value = sampleText;
    // highlightVowelsRealtime();
});

// Enhanced vowel checking function
function isVowel(char) {
    const vowels = ['a', 'e', 'i', 'o', 'u'];
    return vowels.includes(char.toLowerCase());
}

// Get vowel type for color coding
function getVowelType(char) {
    return char.toLowerCase();
}

// Real-time vowel highlighting function
function highlightVowelsRealtime() {
    const text = document.getElementById('inputText').value;
    const highlightedTextDiv = document.getElementById('highlightedText');

    if (!text.trim()) {
        highlightedTextDiv.innerHTML = 'Start typing to see vowels highlighted...';
        updateStatistics('');
        return;
    }

    let highlightedHTML = '';

    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        if (isVowel(char)) {
            const vowelType = getVowelType(char);
            highlightedHTML += `<span class="vowel ${vowelType}-vowel" style="color: ${vowelColors[vowelType]}; font-weight: bold;">${char}</span>`;
        } else {
            highlightedHTML += char;
        }
    }

    highlightedTextDiv.innerHTML = highlightedHTML;
    updateStatistics(text);
}

// Update statistics
function updateStatistics(text) {
    const vowelCounts = { a: 0, e: 0, i: 0, o: 0, u: 0 };
    let totalVowels = 0;

    for (let char of text.toLowerCase()) {
        if (isVowel(char)) {
            vowelCounts[char]++;
            totalVowels++;
        }
    }

    // Update individual vowel counts
    document.getElementById('countA').textContent = vowelCounts.a;
    document.getElementById('countE').textContent = vowelCounts.e;
    document.getElementById('countI').textContent = vowelCounts.i;
    document.getElementById('countO').textContent = vowelCounts.o;
    document.getElementById('countU').textContent = vowelCounts.u;

    // Update total statistics
    document.getElementById('totalVowels').textContent = totalVowels;
    document.getElementById('totalChars').textContent = text.length;

    const percentage = text.length > 0 ? ((totalVowels / text.length) * 100).toFixed(1) : 0;
    document.getElementById('vowelPercentage').textContent = percentage + '%';
}

// Text-to-speech functionality
function readText() {
    const text = document.getElementById('inputText').value;
    const readBtn = document.getElementById('readBtn');

    if (!text.trim()) {
        alert('Please enter some text to read!');
        return;
    }

    // Stop current speech if playing
    if (speechSynthesis.speaking) {
        speechSynthesis.cancel();
        readBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        readBtn.classList.remove('speaking');
        return;
    }

    // Create new utterance
    currentUtterance = new SpeechSynthesisUtterance(text);

    // Configure speech settings
    currentUtterance.rate = 0.8;
    currentUtterance.pitch = 1;
    currentUtterance.volume = 1;

    // Event listeners
    currentUtterance.onstart = function() {
        readBtn.innerHTML = '<i class="fas fa-stop"></i>';
        readBtn.classList.add('speaking');
    };

    currentUtterance.onend = function() {
        readBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        readBtn.classList.remove('speaking');
    };

    currentUtterance.onerror = function() {
        readBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        readBtn.classList.remove('speaking');
        alert('Speech synthesis failed. Please try again.');
    };

    // Start speaking
    speechSynthesis.speak(currentUtterance);
}

// Clear text function
function clearText() {
    if (confirm('Are you sure you want to clear all text?')) {
        document.getElementById('inputText').value = '';
        document.getElementById('highlightedText').innerHTML = 'Start typing to see vowels highlighted...';
        updateStatistics('');
    }
}



// Theme toggle function
function toggleTheme() {
    isDarkTheme = !isDarkTheme;
    const theme = isDarkTheme ? 'dark' : 'light';
    document.body.setAttribute('data-theme', theme);

    const themeIcon = document.querySelector('.theme-toggle i');
    themeIcon.className = isDarkTheme ? 'fas fa-moon' : 'fas fa-sun';
}

// Legacy function for backward compatibility
function countVowels() {
    highlightVowelsRealtime();
}