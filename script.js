// Global variables
let isDarkTheme = true;
let speechSynthesis = window.speechSynthesis;
let currentUtterance = null;

// Vowel colors for highlighting
const vowelColors = {
    'a': '#ff6b6b', // Red
    'e': '#4ecdc4', // Teal
    'i': '#45b7d1', // Blue
    'o': '#96ceb4', // Green
    'u': '#feca57'  // Yellow
};

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Set initial theme
    document.body.setAttribute('data-theme', 'dark');

    // Reset all counts and displays
    resetAllCounts();
});

// Enhanced vowel checking function
function isVowel(char) {
    const vowels = ['a', 'e', 'i', 'o', 'u'];
    return vowels.includes(char.toLowerCase());
}

// Get vowel type for color coding
function getVowelType(char) {
    return char.toLowerCase();
}

// Vowel counting function (triggered only by count button)
function analyzeVowels() {
    const text = document.getElementById('inputText').value;
    const highlightedTextDiv = document.getElementById('highlightedText');

    if (!text.trim()) {
        showNotification('Please enter some text to analyze!', 'warning');
        highlightedTextDiv.innerHTML = 'Click "Count Vowels" to analyze your text...';
        resetAllCounts();
        return;
    }

    // Display text with colored vowels (no highlighting, just color change)
    let coloredHTML = '';

    for (let i = 0; i < text.length; i++) {
        const char = text[i];
        if (isVowel(char)) {
            const vowelType = getVowelType(char);
            coloredHTML += `<span style="color: ${vowelColors[vowelType]};">${char}</span>`;
        } else {
            coloredHTML += char;
        }
    }

    highlightedTextDiv.innerHTML = coloredHTML;
    updateStatistics(text);

    // Show success notification
    const totalVowels = getTotalVowelCount(text);
    showNotification(`Analysis complete! Found ${totalVowels} vowel${totalVowels !== 1 ? 's' : ''} in your text.`, 'success');
}

// Get total vowel count for notification
function getTotalVowelCount(text) {
    let count = 0;
    for (let char of text.toLowerCase()) {
        if (isVowel(char)) {
            count++;
        }
    }
    return count;
}

// Reset all counts and displays
function resetAllCounts() {
    // Reset header counts
    document.getElementById('headerCountA').textContent = '0';
    document.getElementById('headerCountE').textContent = '0';
    document.getElementById('headerCountI').textContent = '0';
    document.getElementById('headerCountO').textContent = '0';
    document.getElementById('headerCountU').textContent = '0';

    // Reset total stats
    document.getElementById('totalVowels').textContent = '0';
    document.getElementById('totalChars').textContent = '0';
    document.getElementById('vowelPercentage').textContent = '0%';

    // Reset highlighted text
    document.getElementById('highlightedText').innerHTML = 'Click "Count Vowels" to analyze your text...';
}

// Update statistics
function updateStatistics(text) {
    const vowelCounts = { a: 0, e: 0, i: 0, o: 0, u: 0 };
    let totalVowels = 0;

    for (let char of text.toLowerCase()) {
        if (isVowel(char)) {
            vowelCounts[char]++;
            totalVowels++;
        }
    }

    // Update header vowel counts with wave animation
    updateHeaderCounts(vowelCounts);

    // Update total statistics
    document.getElementById('totalVowels').textContent = totalVowels;
    document.getElementById('totalChars').textContent = text.length;

    const percentage = text.length > 0 ? ((totalVowels / text.length) * 100).toFixed(1) : 0;
    document.getElementById('vowelPercentage').textContent = percentage + '%';
}

// Update header counts with wave animation
function updateHeaderCounts(vowelCounts) {
    const vowels = ['a', 'e', 'i', 'o', 'u'];

    vowels.forEach((vowel, index) => {
        const countElement = document.getElementById(`headerCount${vowel.toUpperCase()}`);
        const indicatorElement = countElement.parentElement;

        // Update count
        countElement.textContent = vowelCounts[vowel];

        // Add wave animation with delay
        setTimeout(() => {
            indicatorElement.classList.add('wave-animation');
            setTimeout(() => {
                indicatorElement.classList.remove('wave-animation');
            }, 600);
        }, index * 100);
    });
}

// Text-to-speech functionality
function readText() {
    const text = document.getElementById('inputText').value;
    const readBtn = document.getElementById('readBtn');

    if (!text.trim()) {
        alert('Please enter some text to read!');
        return;
    }

    // Stop current speech if playing
    if (speechSynthesis.speaking) {
        speechSynthesis.cancel();
        readBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        readBtn.classList.remove('speaking');
        return;
    }

    // Create new utterance
    currentUtterance = new SpeechSynthesisUtterance(text);

    // Configure speech settings
    currentUtterance.rate = 0.8;
    currentUtterance.pitch = 1;
    currentUtterance.volume = 1;

    // Event listeners
    currentUtterance.onstart = function() {
        readBtn.innerHTML = '<i class="fas fa-stop"></i>';
        readBtn.classList.add('speaking');
    };

    currentUtterance.onend = function() {
        readBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        readBtn.classList.remove('speaking');
    };

    currentUtterance.onerror = function() {
        readBtn.innerHTML = '<i class="fas fa-volume-up"></i>';
        readBtn.classList.remove('speaking');
        alert('Speech synthesis failed. Please try again.');
    };

    // Start speaking
    speechSynthesis.speak(currentUtterance);
}

// Clear text function
function clearText() {
    document.getElementById('inputText').value = '';
    resetAllCounts();
    showNotification('Text cleared successfully!', 'info');
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');

    // Clear any existing content and classes
    notification.innerHTML = '';
    notification.className = `notification ${type}`;

    // Add icon and message
    const iconMap = {
        'success': '✓',
        'warning': '⚠',
        'error': '✕',
        'info': 'ℹ'
    };

    notification.innerHTML = `<span class="notification-icon">${iconMap[type] || 'ℹ'}</span><span class="notification-message">${message}</span>`;

    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);

    // Auto-hide after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
    }, 3000);
}



// Theme toggle function
function toggleTheme() {
    isDarkTheme = !isDarkTheme;
    const theme = isDarkTheme ? 'dark' : 'light';
    document.body.setAttribute('data-theme', theme);

    const themeIcon = document.querySelector('.theme-toggle i');
    themeIcon.className = isDarkTheme ? 'fas fa-moon' : 'fas fa-sun';
}

// Main count function (triggered by count button)
function countVowels() {
    analyzeVowels();
}