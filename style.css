body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
    padding: 0;
    background-color: #0d1117;
    color: white;
    text-align: center;
    min-height: 100vh;
    display: grid;
    place-items: center;
    align-content: center;
    margin-inline: 2rem;
}

* {
    padding: 0;
    margin: 0;
    box-sizing: border-box;
}

main {
    max-width: 20rem;
    width: 100%;
    padding: 1rem;
    background-color: #161b22;
    border-radius: 0.375rem;
    border: 0.0625rem solid #2c3239;
    margin-top: 1rem;
}

h1 {
    font-weight: lighter;
}

textarea {
    resize: none;
}

textarea:focus {
    outline: none;
}

#inputText {
    width: 100%;
    display: block;
    font-size: 1rem;
    line-height: 1.5;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif;
    font-weight: normal;
    border: 0.0625rem solid #2c3239;
    color: white;
    border-radius: 0.375rem;
    background-color: #0d1117;
    padding: .5rem 1rem;
    margin-block: .5rem;
}

#inputText:focus {
    outline: 0.0625rem solid #2f81f7;
}

textarea::-webkit-scrollbar {
    display: none;
}

button {
    display: block;
    width: 100%;
    background-color: #238636;
    border: none;
    color: white;
    cursor: pointer;
    padding: 0.5rem 1rem;
    font-weight: bold;
    border-radius: 0.375rem;
    margin-top: 1rem;
}

button:hover {
    background-color: #2ea043;
}

button:active {
    background-color: #238636;
}

#result {
    width: 100%;
    max-width: 20rem;
    margin-top: 1rem;
    border-radius: 0.375rem;
    padding: 0.5rem 1rem;
    border: 0.0625rem solid #2c3239;
    font-weight: bold;
}
