/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables for theming */
:root {
    --primary-color: #6366f1;
    --primary-hover: #5b5bd6;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    /* Dark theme colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --border-color: #475569;
    --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
}

/* Light theme */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e2e8f0;
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #cbd5e1;
    --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

/* Body and container */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    min-height: 100vh;
    line-height: 1.6;
    transition: all 0.3s ease;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header styles */
header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding: 1.5rem 0;
    border-bottom: 2px solid var(--border-color);
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-content h1 i {
    color: var(--accent-color);
    -webkit-text-fill-color: var(--accent-color);
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 400;
}

.theme-toggle {
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
    box-shadow: var(--shadow);
}

/* Main content */
main {
    flex: 1;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: start;
}

/* Input section */
.input-section {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 1rem;
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.input-section:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.input-header label {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.controls {
    display: flex;
    gap: 0.5rem;
}

.icon-btn {
    background: var(--primary-color);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.icon-btn.speaking {
    background: var(--error-color);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

#inputText {
    width: 100%;
    min-height: 150px;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: inherit;
    resize: vertical;
    transition: all 0.3s ease;
}

#inputText:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

#inputText::placeholder {
    color: var(--text-muted);
}

/* Output section */
.output-section {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.highlighted-text-container,
.statistics {
    background: var(--bg-secondary);
    padding: 2rem;
    border-radius: 1rem;
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
}

.highlighted-text-container:hover,
.statistics:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.highlighted-text-container h3,
.statistics h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.highlighted-text {
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 2px solid var(--border-color);
    min-height: 120px;
    line-height: 1.8;
    font-size: 1rem;
    color: var(--text-primary);
    word-wrap: break-word;
    margin-bottom: 1rem;
}

.copy-btn {
    background: var(--accent-color);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: fit-content;
}

.copy-btn:hover {
    background: #0891b2;
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

/* Statistics styles */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 0.75rem;
    border: 2px solid var(--border-color);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow);
}

.vowel-letter {
    font-size: 1.5rem;
    font-weight: bold;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    color: white;
}

.a-vowel { background-color: #ff6b6b; }
.e-vowel { background-color: #4ecdc4; }
.i-vowel { background-color: #45b7d1; }
.o-vowel { background-color: #96ceb4; }
.u-vowel { background-color: #feca57; }

.count {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.total-stats {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    padding: 1.5rem;
    background: var(--bg-primary);
    border-radius: 0.75rem;
    border: 2px solid var(--border-color);
}

.total-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 1rem;
}

.total-item span:first-child {
    color: var(--text-secondary);
    font-weight: 500;
}

.total-item span:last-child {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.1rem;
}

/* Vowel highlighting in text */
.vowel {
    display: inline-block;
    margin: 0 1px;
    border-radius: 3px;
    transition: all 0.2s ease;
    animation: highlightFade 0.5s ease-in-out;
}

@keyframes highlightFade {
    0% { transform: scale(1.2); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

/* Footer */
footer {
    margin-top: 3rem;
    padding: 2rem 0;
    text-align: center;
    border-top: 2px solid var(--border-color);
    color: var(--text-secondary);
}

footer p {
    font-size: 1rem;
    font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }

    header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    main {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .input-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .controls {
        justify-content: center;
    }

    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 0.75rem;
    }

    .stat-item {
        padding: 0.75rem;
    }

    .vowel-letter {
        font-size: 1.2rem;
        width: 35px;
        height: 35px;
    }
}

@media (max-width: 480px) {
    .header-content h1 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .input-section,
    .highlighted-text-container,
    .statistics {
        padding: 1.5rem;
    }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Loading and transition effects */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Focus styles for accessibility */
button:focus,
.icon-btn:focus,
.theme-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

#inputText:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}
