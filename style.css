/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Variables for theming */
:root {
    --primary-color: #6366f1;
    --primary-hover: #5b5bd6;
    --secondary-color: #8b5cf6;
    --accent-color: #06b6d4;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;

    /* Dark theme colors */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-muted: #64748b;
    --border-color: #475569;
    --shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.3);
    --shadow-lg: 0 25px 50px -12px rgba(0, 0, 0, 0.4);
}

/* Light theme */
[data-theme="light"] {
    --bg-primary: #ffffff;
    --bg-secondary: #fefefe;
    --bg-tertiary: #f5f5f5;
    --text-primary: #2d3748;
    --text-secondary: #4a5568;
    --text-muted: #718096;
    --border-color: #e2e8f0;
    --shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.05);
    --shadow-lg: 0 4px 16px -4px rgba(0, 0, 0, 0.08);
}

/* Body and container */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    color: var(--text-primary);
    min-height: 100vh;
    line-height: 1.6;
    transition: all 0.3s ease;
    position: relative;
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    font-size: 0.9rem;
    z-index: 1000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification.success {
    background: #10b981;
    color: white;
}

.notification.warning {
    background: #f59e0b;
    color: white;
}

.notification.error {
    background: #ef4444;
    color: white;
}

.notification.info {
    background: #06b6d4;
    color: white;
}

.notification.success::before {
    content: "✓";
}

.notification.warning::before {
    content: "⚠";
}

.notification.error::before {
    content: "✕";
}

.notification.info::before {
    content: "ℹ";
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem;
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* Header styles */
header {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 1.5rem;
    padding: 1rem 0;
    border-bottom: 2px solid var(--border-color);
    position: relative;
}

.header-content {
    text-align: center;
}

.header-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.header-content h1 i {
    color: var(--accent-color);
    -webkit-text-fill-color: var(--accent-color);
}

.subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 400;
    margin-bottom: 1rem;
}

/* Vowel indicators in header */
.vowel-indicators {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    margin-top: 1rem;
}

.vowel-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.vowel-indicator.wave-animation {
    animation: waveEffect 0.6s ease-in-out;
}

@keyframes waveEffect {
    0%, 100% { transform: translateY(0) scale(1); }
    25% { transform: translateY(-8px) scale(1.1); }
    50% { transform: translateY(-12px) scale(1.15); }
    75% { transform: translateY(-8px) scale(1.1); }
}

.theme-toggle {
    background: var(--bg-tertiary);
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1.2rem;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
}

.theme-toggle:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow);
}

/* Main content */
main {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    overflow: hidden;
}

.top-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    flex: 1;
    min-height: 0;
}

/* Input section */
.input-section {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 1rem;
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.input-section:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.input-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.input-header label {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
}

.controls {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.icon-btn {
    background: var(--primary-color);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.icon-btn:hover {
    background: var(--primary-hover);
    transform: scale(1.05);
}

.count-btn {
    background: var(--success-color);
    border: none;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    white-space: nowrap;
    width: 100%;
    margin-top: 1rem;
}

.count-btn:hover {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.icon-btn.speaking {
    background: var(--error-color);
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

#inputText {
    width: 100%;
    flex: 1;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: 0.75rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    font-family: inherit;
    resize: none;
    transition: all 0.3s ease;
}

#inputText:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

#inputText::placeholder {
    color: var(--text-muted);
}

/* Highlighted text container */
.highlighted-text-container {
    background: var(--bg-secondary);
    padding: 1.5rem;
    border-radius: 1rem;
    border: 2px solid var(--border-color);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.highlighted-text-container:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.highlighted-text-container h3 {
    color: var(--text-primary);
    margin-bottom: 1rem;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.highlighted-text {
    background: var(--bg-primary);
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 2px solid var(--border-color);
    flex: 1;
    line-height: 1.8;
    font-size: 1rem;
    color: var(--text-primary);
    word-wrap: break-word;
    overflow-y: auto;
}

/* Vowel letter styles for header */
.vowel-letter {
    font-size: 1.2rem;
    font-weight: bold;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s ease;
}

.a-vowel { background-color: #ff6b6b; }
.e-vowel { background-color: #4ecdc4; }
.i-vowel { background-color: #45b7d1; }
.o-vowel { background-color: #96ceb4; }
.u-vowel { background-color: #feca57; }

.count {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.total-stats {
    display: flex;
    justify-content: space-around;
    gap: 1rem;
    padding: 1rem;
    background: var(--bg-primary);
    border-radius: 0.75rem;
    border: 2px solid var(--border-color);
    margin-top: 1rem;
}

.total-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.9rem;
}

.total-item span:first-child {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.8rem;
}

.total-item span:last-child {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.1rem;
}

/* Vowel highlighting in text */
.vowel {
    display: inline;
    transition: all 0.2s ease;
    animation: highlightFade 0.5s ease-in-out;
}

@keyframes highlightFade {
    0% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Responsive design */
@media (max-width: 768px) {
    .container {
        padding: 0.5rem;
    }

    header {
        margin-bottom: 1rem;
        padding: 0.5rem 0;
    }

    .header-content h1 {
        font-size: 2rem;
    }

    .top-section {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .input-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .controls {
        justify-content: center;
    }

    .vowel-indicators {
        gap: 1rem;
    }

    .vowel-letter {
        font-size: 1rem;
        width: 30px;
        height: 30px;
    }

    .input-section,
    .highlighted-text-container {
        padding: 1rem;
    }

    .total-stats {
        flex-direction: column;
        gap: 0.5rem;
    }

    .total-item {
        flex-direction: row;
        justify-content: space-between;
    }
}

@media (max-width: 480px) {
    .header-content h1 {
        font-size: 1.5rem;
        flex-direction: column;
        gap: 0.5rem;
    }

    .vowel-indicators {
        gap: 0.75rem;
        flex-wrap: wrap;
    }

    .vowel-letter {
        font-size: 0.9rem;
        width: 28px;
        height: 28px;
    }

    .theme-toggle {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .controls {
        justify-content: center;
    }

    .notification {
        top: 10px;
        right: 10px;
        left: 10px;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Loading and transition effects */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Focus styles for accessibility */
button:focus,
.icon-btn:focus,
.theme-toggle:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

#inputText:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}
